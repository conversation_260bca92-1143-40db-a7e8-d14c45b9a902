/**
 * @file pipeline_manager_test.cpp
 * @brief Unit tests for pipeline manager functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>

namespace omop::core {

using namespace testing;

class MockPipeline : public ETLPipeline {
public:
    MockPipeline() : ETLPipeline() {}
    
    MOCK_METHOD(std::future<JobInfo>, start, (const std::string&), (override));
    MOCK_METHOD(void, stop, (), (override));
    MOCK_METHOD(void, pause, (), (override));
    MOCK_METHOD(void, resume, (), (override));
    MOCK_METHOD(JobStatus, get_status, (), (const, override));
    MOCK_METHOD(JobInfo, get_job_info, (), (const, override));
};

class PipelineManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager = std::make_unique<PipelineManager>(2); // Max 2 concurrent jobs
    }
    
    std::unique_ptr<PipelineManager> manager;
};

// Test basic job submission
// Test that PipelineManager can submit jobs and track their completion status
TEST_F(PipelineManagerTest, SubmitJob) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    // Set up expectations
    JobInfo mock_info;
    mock_info.status = JobStatus::Completed;
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    promise->set_value(mock_info);
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    EXPECT_FALSE(job_id.empty());
    EXPECT_TRUE(job_id.starts_with("job-"));
}

// Test job status retrieval
TEST_F(PipelineManagerTest, GetJobStatus) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    JobInfo mock_info;
    mock_info.status = JobStatus::Running;
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    auto status = manager->get_job_status(job_id);
    ASSERT_TRUE(status.has_value());
    // Note: Status might be Created or Running depending on timing
}

// Test job info retrieval
// Test that PipelineManager can retrieve job information by ID
TEST_F(PipelineManagerTest, GetJobInfo) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    JobInfo mock_info;
    mock_info.job_name = "test_job";
    mock_info.status = JobStatus::Created;
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    auto job_info = manager->get_job_info(job_id);
    ASSERT_TRUE(job_info.has_value());
    EXPECT_EQ(job_info->job_name, "test_job");
}

// Test getting all jobs
// Test that PipelineManager returns all submitted jobs
TEST_F(PipelineManagerTest, GetAllJobs) {
    // Submit multiple jobs
    for (int i = 0; i < 3; ++i) {
        auto pipeline = std::make_unique<MockPipeline>();
        
        auto promise = std::make_shared<std::promise<JobInfo>>();
        auto future = promise->get_future();
        
        EXPECT_CALL(*pipeline, start(_))
            .WillOnce(Return(std::move(future)));
        
        manager->submit_job("job_" + std::to_string(i), std::move(pipeline));
    }
    
    auto all_jobs = manager->get_all_jobs();
    EXPECT_EQ(all_jobs.size(), 3);
}

// Test job cancellation
// Test that PipelineManager can cancel running jobs
TEST_F(PipelineManagerTest, CancelJob) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    EXPECT_CALL(*pipeline, stop())
        .Times(AtLeast(1));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    bool cancelled = manager->cancel_job(job_id);
    EXPECT_TRUE(cancelled);
    
    auto status = manager->get_job_status(job_id);
    if (status) {
        EXPECT_EQ(*status, JobStatus::Cancelled);
    }
}

// Test job pausing
TEST_F(PipelineManagerTest, PauseJob) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    EXPECT_CALL(*pipeline, pause())
        .Times(AtLeast(1));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    // Wait a bit for job to start
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    bool paused = manager->pause_job(job_id);
    EXPECT_TRUE(paused);
}

// Test job resuming
TEST_F(PipelineManagerTest, ResumeJob) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    EXPECT_CALL(*pipeline, pause())
        .Times(AtLeast(1));
    EXPECT_CALL(*pipeline, resume())
        .Times(AtLeast(1));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    // Wait for job to start, then pause and resume
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    manager->pause_job(job_id);
    
    bool resumed = manager->resume_job(job_id);
    EXPECT_TRUE(resumed);
}

// Test waiting for job completion
TEST_F(PipelineManagerTest, WaitForJob) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    JobInfo mock_info;
    mock_info.status = JobStatus::Completed;
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    // Complete the job after a short delay
    std::thread([promise]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        promise->set_value(JobInfo{});
    }).detach();
    
    bool completed = manager->wait_for_job(job_id, 1000); // 1 second timeout
    EXPECT_TRUE(completed);
}

// Test waiting for job with timeout
TEST_F(PipelineManagerTest, WaitForJobTimeout) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    // Don't complete the job
    bool completed = manager->wait_for_job(job_id, 100); // 100ms timeout
    EXPECT_FALSE(completed);
}

// Test manager shutdown
// Test that PipelineManager handles graceful shutdown correctly
TEST_F(PipelineManagerTest, Shutdown) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    manager->submit_job("test_job", std::move(pipeline));
    
    EXPECT_NO_THROW(manager->shutdown(false)); // Don't wait for jobs
}

// Test that PipelineManager rejects null pipeline submissions
TEST_F(PipelineManagerTest, SubmitNullPipelineThrows) {
    EXPECT_THROW(manager->submit_job("test", nullptr), omop::common::ConfigurationException);
}

// Test null pipeline rejection
TEST_F(PipelineManagerTest, RejectsNullPipeline) {
    EXPECT_THROW(manager->submit_job("test_job", nullptr), 
                 common::ConfigurationException);
}

// Test that PipelineManager returns empty optional for non-existent jobs
TEST_F(PipelineManagerTest, GetNonExistentJob) {
    auto status = manager->get_job_status("nonexistent");
    EXPECT_FALSE(status.has_value());
    
    auto info = manager->get_job_info("nonexistent");
    EXPECT_FALSE(info.has_value());
}

// Test that PipelineManager returns false when canceling non-existent job
TEST_F(PipelineManagerTest, CancelNonExistentJob) {
    bool result = manager->cancel_job("nonexistent");
    EXPECT_FALSE(result);
}

// Test nonexistent job operations
TEST_F(PipelineManagerTest, NonexistentJobOperations) {
    std::string fake_job_id = "nonexistent-job";
    
    EXPECT_FALSE(manager->get_job_status(fake_job_id).has_value());
    EXPECT_FALSE(manager->get_job_info(fake_job_id).has_value());
    EXPECT_FALSE(manager->cancel_job(fake_job_id));
    EXPECT_FALSE(manager->pause_job(fake_job_id));
    EXPECT_FALSE(manager->resume_job(fake_job_id));
    EXPECT_FALSE(manager->wait_for_job(fake_job_id, 100));
}

// Test concurrent job submission
TEST_F(PipelineManagerTest, ConcurrentJobSubmission) {
    std::vector<std::thread> threads;
    std::vector<std::string> job_ids;
    std::mutex job_ids_mutex;
    
    // Submit jobs concurrently
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&, i]() {
            auto pipeline = std::make_unique<MockPipeline>();
            
            auto promise = std::make_shared<std::promise<JobInfo>>();
            auto future = promise->get_future();
            
            EXPECT_CALL(*pipeline, start(_))
                .WillOnce(Return(std::move(future)));
            
            std::string job_id = manager->submit_job("job_" + std::to_string(i), 
                                                   std::move(pipeline));
            
            std::lock_guard<std::mutex> lock(job_ids_mutex);
            job_ids.push_back(job_id);
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(job_ids.size(), 10);
    
    // Verify all job IDs are unique
    std::set<std::string> unique_ids(job_ids.begin(), job_ids.end());
    EXPECT_EQ(unique_ids.size(), 10);
}

// Test that PipelineManager can retrieve active jobs from all jobs
TEST_F(PipelineManagerTest, GetActiveJobs) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    auto active_jobs = manager->get_active_jobs();
    bool found = false;
    for (const auto& job : active_jobs) {
        if (job.job_id == job_id) {
            found = true;
            break;
        }
    }
    EXPECT_TRUE(found);
}

// Test that PipelineManager maintains correct job count
TEST_F(PipelineManagerTest, JobCount) {
    EXPECT_EQ(manager->get_all_jobs().size(), 0);

    auto pipeline1 = std::make_unique<MockPipeline>();
    auto pipeline2 = std::make_unique<MockPipeline>();
    
    auto promise1 = std::make_shared<std::promise<JobInfo>>();
    auto promise2 = std::make_shared<std::promise<JobInfo>>();
    
    EXPECT_CALL(*pipeline1, start(_))
        .WillOnce(Return(std::move(promise1->get_future())));
    EXPECT_CALL(*pipeline2, start(_))
        .WillOnce(Return(std::move(promise2->get_future())));
    
    manager->submit_job("job1", std::move(pipeline1));
    manager->submit_job("job2", std::move(pipeline2));
    
    EXPECT_EQ(manager->get_all_jobs().size(), 2);
}

// Test that PipelineManager handles force shutdown correctly
TEST_F(PipelineManagerTest, ForceShutdown) {
    auto pipeline = std::make_unique<MockPipeline>();
    
    auto promise = std::make_shared<std::promise<JobInfo>>();
    auto future = promise->get_future();
    
    EXPECT_CALL(*pipeline, start(_))
        .WillOnce(Return(std::move(future)));
    
    std::string job_id1 = manager->submit_job("test_job1", std::move(pipeline));
    
    auto pipeline2 = std::make_unique<MockPipeline>();
    auto promise2 = std::make_shared<std::promise<JobInfo>>();
    auto future2 = promise2->get_future();
    
    EXPECT_CALL(*pipeline2, start(_))
        .WillOnce(Return(std::move(future2)));
    
    std::string job_id2 = manager->submit_job("test_job2", std::move(pipeline2));
    
    EXPECT_NO_THROW(manager->shutdown(true)); // Force shutdown
    EXPECT_FALSE(job_id1.empty());
    EXPECT_FALSE(job_id2.empty());
}

} // namespace omop::core