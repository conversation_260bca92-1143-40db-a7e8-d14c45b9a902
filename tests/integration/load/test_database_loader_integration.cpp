/**
 * @brief Integration tests for database loader with UK localization
 * Tests database loading functionality including transactions, bulk operations, and OMOP CDM compliance
 */

#include <gtest/gtest.h>
#include "load/database_loader.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "cdm/omop_tables.h"
#include <iomanip>
#include <locale>
#include <thread>
#include <atomic>
#include <barrier>

namespace omop::test {

class DatabaseLoaderIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        logger_ = common::Logger::get("test-database-loader");
        
        // Set UK locale
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (...) {
            std::locale::global(std::locale::classic());
        }

        // Setup database with UK settings
        db_fixture_ = std::make_unique<DatabaseFixture>();
        DatabaseConfig db_config;
        db_config.host = "localhost";
        db_config.port = 5432;
        db_config.database = "omop_test_db";
        db_config.username = "test_user";
        db_config.password = "test_pass";

        try {
            db_fixture_->setup(db_config);
            createTargetTables();
            
            // Set UK timezone for all connections
            auto conn = db_fixture_->get_connection();
            conn->execute_update("SET timezone = 'Europe/London'");
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Database not available: " << e.what();
        }
    }

    void TearDown() override {
        // Ensure all database connections are closed
        cleanup();
        if (db_fixture_) {
            db_fixture_->teardown();
        }
    }

    void createTargetTables() {
        // Create OMOP CDM tables for testing
        db_fixture_->create_omop_tables({
            "person",
            "observation_period",
            "visit_occurrence",
            "condition_occurrence",
            "drug_exposure",
            "measurement"
        });
        
        // Ensure schemas exist
        auto conn = db_fixture_->get_connection();
        conn->execute_update("CREATE SCHEMA IF NOT EXISTS test_cdm");
        
        // Set UK date style
        conn->execute_update("SET datestyle = 'ISO, DMY'");
    }

    void cleanup() {
        try {
            if (db_fixture_ && db_fixture_->get_connection()) {
                auto conn = db_fixture_->get_connection();
                // Clean up test data
                std::vector<std::string> tables = {
                    "person", "observation_period", "visit_occurrence",
                    "condition_occurrence", "drug_exposure", "measurement"
                };
                for (const auto& table : tables) {
                    conn->execute_update("TRUNCATE TABLE test_cdm." + table + " CASCADE");
                }
            }
        } catch (...) {}
    }

    std::unique_ptr<load::DatabaseLoader> createLoader(
        const load::DatabaseLoaderOptions& options = {}) {

        auto conn = db_fixture_->get_connection();
        return std::make_unique<load::DatabaseLoader>(std::move(conn), options);
    }

    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::shared_ptr<common::Logger> logger_;
    
    // UK date/time formatter
    std::string format_uk_datetime(const std::chrono::system_clock::time_point& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%d/%m/%Y %H:%M:%S");
        return ss.str();
    }
};

/**
 * @brief Test basic database loading with UK person data
 */
TEST_F(DatabaseLoaderIntegrationTest, BasicDatabaseLoading) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 100;
    options.use_bulk_insert = true;

    auto loader = createLoader(options);

    // Initialize loader
    std::unordered_map<std::string, std::any> config;
    config["target_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Create test record
    core::Record record;
    record.setField("person_id", int64_t(1001));
    record.setField("gender_concept_id", int32_t(8507));
    record.setField("year_of_birth", int32_t(1980));
    record.setField("month_of_birth", int32_t(3));
    record.setField("day_of_birth", int32_t(15));
    record.setField("race_concept_id", int32_t(8527));
    record.setField("ethnicity_concept_id", int32_t(********));
    record.setField("person_source_value", std::string("PATIENT_1001"));
    
    // Add UK-specific fields
    record.setField("location_source_value", std::string("SW1A 1AA")); // Buckingham Palace
    record.setField("gender_source_value", std::string("M"));
    record.setField("race_source_value", std::string("White British"));

    // Load single record
    bool success = loader->load(record, context);
    EXPECT_TRUE(success);

    // Commit
    loader->commit(context);

    // Verify record was inserted
    EXPECT_EQ(db_fixture_->get_row_count("person", "test_cdm"), 1);

    // Check statistics
    auto stats = loader->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["total_loaded"]), 1);
    EXPECT_EQ(std::any_cast<size_t>(stats["total_failed"]), 0);
    
    logger_->info("Basic loading test completed - UK person record loaded");
}

/**
 * @brief Test batch loading with UK patient cohort
 */
TEST_F(DatabaseLoaderIntegrationTest, BatchLoading) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 50;
    options.use_bulk_insert = true;
    options.commit_interval = 100;
    options.null_string = ""; // UK standard for NULL representation

    auto loader = createLoader(options);

    // Initialize
    std::unordered_map<std::string, std::any> config;
    config["target_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Generate test data
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(200);
    
    // Add UK-specific data to records
    std::vector<std::string> uk_cities = {"London", "Manchester", "Birmingham", "Glasgow", "Cardiff"};
    for (size_t i = 0; i < records.size(); ++i) {
        records[i].setField("location_source_value", 
                           uk_cities[i % uk_cities.size()] + " " + std::to_string(i % 100));
        records[i].setField("nhs_number", std::format("NHS{:09d}", ********0 + i));
    }

    // Create batch
    core::RecordBatch batch;
    for (const auto& record : records) {
        batch.addRecord(record);
    }

    // Load batch
    auto start = std::chrono::high_resolution_clock::now();
    size_t loaded = loader->load_batch(batch, context);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // Format with UK number formatting
    logger_->info("Loaded {:L} records in {:L} ms", loaded, duration.count());
    logger_->info("Throughput: {:L.0f} records/second", 
                 (loaded * 1000.0) / duration.count());

    EXPECT_EQ(loaded, 200);

    // Commit
    loader->commit(context);

    // Verify all records were inserted
    EXPECT_EQ(db_fixture_->get_row_count("person", "test_cdm"), 200);
}

/**
 * @brief Test OMOP-specific loader with UK healthcare codes
 */
TEST_F(DatabaseLoaderIntegrationTest, OmopDatabaseLoader) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 100;

    auto conn = db_fixture_->get_connection();
    auto loader = std::make_unique<load::OmopDatabaseLoader>(std::move(conn), options);

    // Initialize for person table
    std::unordered_map<std::string, std::any> config;
    config["omop_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");
    config["validate_foreign_keys"] = false; // No vocabulary tables

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Create person record with OMOP structure
    core::Record record;
    record.setField("person_id", int64_t(2001));
    record.setField("gender_concept_id", int32_t(8532)); // Female
    record.setField("year_of_birth", int32_t(1975));
    record.setField("month_of_birth", int32_t(7));
    record.setField("day_of_birth", int32_t(22));
    record.setField("birth_datetime", std::chrono::system_clock::now());
    record.setField("race_concept_id", int32_t(8516)); // Black
    record.setField("ethnicity_concept_id", int32_t(38003563)); // Hispanic
    record.setField("person_source_value", std::string("PAT_2001"));
    record.setField("gender_source_value", std::string("F"));
    record.setField("race_source_value", std::string("Black British"));
    record.setField("ethnicity_source_value", std::string("Hispanic"));
    
    // UK-specific OMOP extensions
    record.setField("location_source_value", std::string("M1 1AA")); // Manchester
    
    // Add UK birth datetime with proper timezone
    std::tm tm_struct{0, 0, 12, 22, 6, 75, 0, 0, 0, 0, nullptr}; // 22/07/1975 12:00 BST
    auto birth_dt = std::chrono::system_clock::from_time_t(
        std::mktime(&tm_struct));
    record.setField("birth_datetime", birth_dt);

    // Load record
    bool success = loader->load(record, context);
    EXPECT_TRUE(success);

    loader->commit(context);

    // Verify record
    auto result = db_fixture_->get_connection()->execute_query(
        "SELECT * FROM test_cdm.person WHERE person_id = 2001");
    EXPECT_TRUE(result->next());
    EXPECT_EQ(std::any_cast<int32_t>(result->get_value("gender_concept_id")), 8532);
    EXPECT_EQ(std::any_cast<int32_t>(result->get_value("year_of_birth")), 1975);
    EXPECT_EQ(std::any_cast<int32_t>(result->get_value("month_of_birth")), 7);
    EXPECT_EQ(std::any_cast<int32_t>(result->get_value("day_of_birth")), 22);
    
    logger_->info("OMOP loader test completed - UK person record validated");
}

/**
 * @brief Test transaction handling with UK banking analogy
 */
TEST_F(DatabaseLoaderIntegrationTest, TransactionHandling) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 10;
    options.use_bulk_insert = false; // Use regular inserts for transaction testing

    auto loader = createLoader(options);

    // Initialize
    std::unordered_map<std::string, std::any> config;
    config["target_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");

    core::ProcessingContext context;
    loader->initialize(config, context);

    // UK bank account numbers for testing transaction integrity
    std::vector<std::string> uk_sort_codes = {
        "20-30-40", "11-22-33", "44-55-66", "77-88-99", "12-34-56"
    };

    // Load some records
    for (int i = 1; i <= 5; ++i) {
        core::Record record;
        record.setField("person_id", int64_t(3000 + i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1985 + i));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(********));
        record.setField("person_source_value", 
                       std::format("BANK-{}-{}", uk_sort_codes[i-1], ******** + i));

        loader->load(record, context);
    }

    // Rollback (simulate failed transaction)
    loader->rollback(context);

    // Verify no records were inserted
    auto initial_count = db_fixture_->get_row_count("person", "test_cdm");
    
    logger_->info("Transaction rolled back - {} records before rollback", initial_count);

    // Load again and commit
    for (int i = 1; i <= 3; ++i) {
        core::Record record;
        record.setField("person_id", int64_t(3000 + i));
        record.setField("gender_concept_id", int32_t(8507));
        record.setField("year_of_birth", int32_t(1985 + i));
        record.setField("race_concept_id", int32_t(8527));
        record.setField("ethnicity_concept_id", int32_t(********));
        record.setField("person_source_value", 
                       std::format("BANK-{}-{}", uk_sort_codes[i-1], ******** + i));

        loader->load(record, context);
    }

    loader->commit(context);

    // Verify only committed records were inserted
    auto final_count = db_fixture_->get_row_count("person", "test_cdm");
    EXPECT_EQ(final_count - initial_count, 3);
    
    logger_->info("Transaction committed - {} new records added", final_count - initial_count);
}

/**
 * @brief Test error handling with UK data validation
 */
TEST_F(DatabaseLoaderIntegrationTest, ErrorHandling) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 10;
    
    // Note: continue_on_error not in DatabaseLoaderOptions, simulate with try-catch

    auto loader = createLoader(options);

    // Initialize
    std::unordered_map<std::string, std::any> config;
    config["target_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Create records with some invalid data
    std::vector<core::Record> records;
    
    // UK postcode validation pattern
    auto is_valid_uk_postcode = [](const std::string& postcode) {
        return std::regex_match(postcode, std::regex(R"([A-Z]{1,2}[0-9][A-Z0-9]? [0-9][A-Z]{2})"));
    };

    // Valid record
    core::Record valid1;
    valid1.setField("person_id", int64_t(4001));
    valid1.setField("gender_concept_id", int32_t(8507));
    valid1.setField("year_of_birth", int32_t(1985));
    valid1.setField("race_concept_id", int32_t(8527));
    valid1.setField("ethnicity_concept_id", int32_t(********));
    valid1.setField("location_source_value", std::string("SW1A 2AA"));
    records.push_back(valid1);

    // Invalid record - missing required field
    core::Record invalid1;
    invalid1.setField("person_id", int64_t(4002));
    // Missing gender_concept_id (required field)
    invalid1.setField("year_of_birth", int32_t(1986));
    invalid1.setField("race_concept_id", int32_t(8527));
    invalid1.setField("ethnicity_concept_id", int32_t(********));
    invalid1.setField("location_source_value", std::string("INVALID")); // Invalid UK postcode
    records.push_back(invalid1);

    // Invalid record - duplicate primary key
    core::Record invalid2;
    invalid2.setField("person_id", int64_t(4001)); // Duplicate
    invalid2.setField("gender_concept_id", int32_t(8532));
    invalid2.setField("year_of_birth", int32_t(1987));
    invalid2.setField("race_concept_id", int32_t(8516));
    invalid2.setField("ethnicity_concept_id", int32_t(38003563));
    invalid2.setField("location_source_value", std::string("EC1A 1BB"));
    records.push_back(invalid2);

    // Valid record
    core::Record valid2;
    valid2.setField("person_id", int64_t(4003));
    valid2.setField("gender_concept_id", int32_t(8532));
    valid2.setField("year_of_birth", int32_t(1988));
    valid2.setField("race_concept_id", int32_t(8527));
    valid2.setField("ethnicity_concept_id", int32_t(********));
    valid2.setField("location_source_value", std::string("G1 1AA")); // Glasgow
    records.push_back(valid2);

    // Try to load all records
    size_t loaded = 0;
    size_t failed = 0;

    for (const auto& record : records) {
        try {
            if (loader->load(record, context)) {
                loaded++;
            } else {
                failed++;
            }
        } catch (const std::exception& e) {
            logger_->warn("Expected error for record: {}", e.what());
            failed++;
        }
    }

    loader->commit(context);

    // Should have loaded valid records only
    EXPECT_GE(loaded, 2); // At least the valid records
    EXPECT_GE(failed, 1); // At least one failure (duplicate key)

    // Verify database state
    auto count = db_fixture_->get_row_count("person", "test_cdm");
    EXPECT_GE(count, 2);

    // Check error statistics
    auto stats = loader->get_statistics();
    if (stats.find("total_failed") != stats.end()) {
        EXPECT_GT(std::any_cast<size_t>(stats["total_failed"]), 0);
    }
    
    logger_->info("Error handling test: {} loaded, {} failed", loaded, failed);
}

/**
 * @brief Test bulk copy performance with UK population data
 */
TEST_F(DatabaseLoaderIntegrationTest, BulkCopyPerformance) {
    load::DatabaseLoaderOptions options;
    options.batch_size = 1000;
    options.use_bulk_insert = true;
    
    // Note: bulk_copy_threshold not in DatabaseLoaderOptions

    auto loader = createLoader(options);

    // Initialize
    std::unordered_map<std::string, std::any> config;
    config["target_table"] = std::string("person");
    config["schema"] = std::string("test_cdm");

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Generate large dataset
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(5000);
    
    // Add UK regional distribution
    std::vector<std::pair<std::string, double>> uk_regions = {
        {"London", 0.15},
        {"South East", 0.14},
        {"North West", 0.11},
        {"East of England", 0.10},
        {"West Midlands", 0.09},
        {"South West", 0.09},
        {"Yorkshire", 0.08},
        {"East Midlands", 0.07},
        {"Scotland", 0.08},
        {"Wales", 0.05},
        {"Northern Ireland", 0.03},
        {"North East", 0.01}
    };
    
    // Assign regions based on distribution
    for (size_t i = 0; i < records.size(); ++i) {
        size_t region_idx = i % uk_regions.size();
        records[i].setField("region_source_value", uk_regions[region_idx].first);
    }

    // Convert to record batch
    core::RecordBatch batch;
    for (const auto& record : records) {
        batch.addRecord(record);
    }

    // Load batch
    auto start = std::chrono::high_resolution_clock::now();
    size_t loaded = loader->load_batch(batch, context);
    auto end = std::chrono::high_resolution_clock::now();

    loader->commit(context);

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    double records_per_second = (loaded * 1000.0) / duration.count();

    logger_->info("Bulk copy performance (UK population data):");
    logger_->info("  - Records: {:L}", loaded);
    logger_->info("  - Time: {:L} ms", duration.count());
    logger_->info("  - Throughput: {:L.0f} records/second", 
                 static_cast<size_t>(records_per_second));

    EXPECT_EQ(loaded, 5000);
    
    // Performance expectation (adjusted for safety)
    EXPECT_GT(records_per_second, 500) << "Bulk loading performance below threshold";
}

/**
 * @brief Test parallel loading with UK NHS trust distribution
 */
TEST_F(DatabaseLoaderIntegrationTest, ParallelLoading) {
    const int num_threads = 4;
    const int records_per_thread = 250;
    
    // UK NHS trusts for parallel loading test
    std::vector<std::string> nhs_trusts = {
        "Guy's and St Thomas'",
        "Imperial College Healthcare",
        "Manchester University",
        "Birmingham Women's and Children's"
    };

    std::vector<std::thread> threads;
    std::atomic<size_t> total_loaded(0);
    std::atomic<size_t> total_failed(0);

    // Create barrier for synchronized start
    std::atomic<int> ready_count{0};
    std::mutex start_mutex;
    std::condition_variable start_cv;

    for (int thread_id = 0; thread_id < num_threads; ++thread_id) {
        threads.emplace_back([this, thread_id, &total_loaded, &total_failed, 
                             &ready_count, &start_mutex, &start_cv, &nhs_trusts, 
                             records_per_thread]() {
            // Create loader for this thread
            load::DatabaseLoaderOptions options;
            options.batch_size = 50;
            options.use_bulk_insert = true;

            auto loader = createLoader(options);

            // Initialize
            std::unordered_map<std::string, std::any> config;
            config["target_table"] = std::string("person");
            config["schema"] = std::string("test_cdm");

            core::ProcessingContext context;
            loader->initialize(config, context);

            // Generate unique records for this thread
            TestDataGenerator generator;
                    auto records = generator.generate_patient_records(records_per_thread);
            
            // Assign NHS trust
            for (auto& record : records) {
                record.setField("care_site_source_value", nhs_trusts[thread_id]);
                record.setField("provider_source_value", 
                               std::format("NHS-{}-{}", thread_id + 1, 
                                         std::rand() % 1000));
            }

            // Wait for all threads to be ready
            {
                std::unique_lock<std::mutex> lock(start_mutex);
                ready_count++;
                if (ready_count == num_threads) {
                    start_cv.notify_all();
                } else {
                    start_cv.wait(lock, [&ready_count] { return ready_count == num_threads; });
                }
            }

            // Load records
            for (const auto& record : records) {
                if (loader->load(record, context)) {
                    total_loaded++;
                } else {
                    total_failed++;
                }
            }

            loader->commit(context);
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    logger_->info("Parallel loading results (UK NHS trusts):");
    logger_->info("  - Total loaded: {:L}", total_loaded.load());
    logger_->info("  - Total failed: {:L}", total_failed.load());

    // Verify results
    EXPECT_EQ(total_loaded.load(), num_threads * records_per_thread);
    EXPECT_EQ(total_failed.load(), 0);

    // Verify database
    auto count = db_fixture_->get_row_count("person", "test_cdm");
    EXPECT_GE(count, total_loaded.load());
}

} // namespace omop::test