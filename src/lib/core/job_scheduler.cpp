/**
 * @file job_scheduler.cpp
 * @brief Implementation of job scheduling system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "job_scheduler.h"
#include "common/utilities.h"
#include "common/exceptions.h"
#include <algorithm>
#include <regex>
#include <sstream>
#include <optional>
#include <any>

namespace omop::core {

// JobComparator implementation
bool JobScheduler::JobComparator::operator()(const QueuedJob& a, const Queued<PERSON><PERSON>& b) const {
    switch (strategy) {
        case SchedulingStrategy::FIFO:
            // Earlier enqueue time has higher priority
            return a.enqueue_time > b.enqueue_time;

        case SchedulingStrategy::PRIORITY:
            // Higher priority value has higher priority
            if (a.priority != b.priority) {
                return static_cast<int>(a.priority) < static_cast<int>(b.priority);
            }
            // For same priority, use FIFO
            return a.enqueue_time > b.enqueue_time;

        case SchedulingStrategy::DEADLINE:
            // Earlier deadline has higher priority
            if (a.deadline != b.deadline) {
                return a.deadline > b.deadline;
            }
            // For same deadline, use priority
            return static_cast<int>(a.priority) < static_cast<int>(b.priority);

        case SchedulingStrategy::ROUND_ROBIN:
        case SchedulingStrategy::FAIR_SHARE:
            // These strategies require more complex state tracking
            // For now, fall back to FIFO
            return a.enqueue_time > b.enqueue_time;

        default:
            return a.enqueue_time > b.enqueue_time;
    }
}

// JobScheduler implementation
JobScheduler::JobScheduler(std::shared_ptr<JobManager> job_manager,
                           SchedulingStrategy strategy)
    : job_manager_(std::move(job_manager)),
      strategy_(strategy),
      job_queue_(JobComparator{strategy}),
      running_(false) {

    if (!job_manager_) {
        throw common::ConfigurationException("Job manager cannot be null", "job_manager");
    }
}

JobScheduler::~JobScheduler() {
    stop();
}

bool JobScheduler::start() {
    if (running_.exchange(true)) {
        return false; // Already running
    }

    // Start scheduler thread
    scheduler_thread_ = std::thread(&JobScheduler::schedulerLoop, this);

    // Register callback with job manager
    job_manager_->registerJobEventCallback(
        [this](const std::string& job_id, [[maybe_unused]] JobStatus old_status, JobStatus new_status) {
            if (new_status == JobStatus::Completed ||
                new_status == JobStatus::Failed ||
                new_status == JobStatus::Cancelled) {

                // Record completion
                {
                    std::lock_guard<std::mutex> lock(completed_mutex_);
                    completed_jobs_[job_id] = new_status;
                }

                // Notify callbacks
                {
                    std::lock_guard<std::mutex> lock(callbacks_mutex_);
                    for (const auto& callback : completion_callbacks_) {
                        callback(job_id, new_status);
                    }
                }

                // Update statistics
                if (new_status == JobStatus::Completed) {
                    jobs_executed_++;
                } else if (new_status == JobStatus::Failed) {
                    jobs_failed_++;
                }
            }
        });

    return true;
}

void JobScheduler::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }

    // Wake up scheduler thread
    queue_cv_.notify_all();

    // Wait for thread to finish
    if (scheduler_thread_.joinable()) {
        scheduler_thread_.join();
    }
}

std::string JobScheduler::addSchedule(const JobSchedule& schedule) {
    JobSchedule new_schedule = schedule;
    if (new_schedule.schedule_id.empty()) {
        new_schedule.schedule_id = omop::common::CryptoUtils::generate_uuid();
    }

    // Calculate next run time if scheduled
    if (new_schedule.trigger_type == TriggerType::SCHEDULED &&
        !new_schedule.cron_expression.empty()) {
        new_schedule.next_run = calculateNextRunTime(
            new_schedule.cron_expression,
            std::chrono::system_clock::now()
        );
    }

    {
        std::lock_guard<std::mutex> lock(schedules_mutex_);
        schedules_[new_schedule.schedule_id] = new_schedule;
    }

    return new_schedule.schedule_id;
}

bool JobScheduler::removeSchedule(const std::string& schedule_id) {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    return schedules_.erase(schedule_id) > 0;
}

bool JobScheduler::updateSchedule(const std::string& schedule_id, const JobSchedule& schedule) {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    auto it = schedules_.find(schedule_id);
    if (it == schedules_.end()) {
        return false;
    }

    JobSchedule updated = schedule;
    updated.schedule_id = schedule_id;

    // Recalculate next run time if needed
    if (updated.trigger_type == TriggerType::SCHEDULED &&
        !updated.cron_expression.empty()) {
        updated.next_run = calculateNextRunTime(
            updated.cron_expression,
            std::chrono::system_clock::now()
        );
    }

    it->second = updated;
    return true;
}

bool JobScheduler::setScheduleEnabled(const std::string& schedule_id, bool enabled) {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    auto it = schedules_.find(schedule_id);
    if (it == schedules_.end()) {
        return false;
    }

    it->second.enabled = enabled;
    return true;
}

std::optional<JobSchedule> JobScheduler::getSchedule(const std::string& schedule_id) const {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    auto it = schedules_.find(schedule_id);
    if (it != schedules_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::vector<JobSchedule> JobScheduler::getAllSchedules() const {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    std::vector<JobSchedule> result;
    result.reserve(schedules_.size());

    for (const auto& [id, schedule] : schedules_) {
        result.push_back(schedule);
    }

    return result;
}

std::string JobScheduler::submitJob(const JobConfig& job_config,
                                   JobPriority priority,
                                   const std::vector<std::string>& dependencies) {
    QueuedJob queued_job;
    queued_job.job_id = omop::common::CryptoUtils::generate_uuid();
    queued_job.job_config = job_config;
    queued_job.job_config.job_id = queued_job.job_id;
    queued_job.priority = priority;
    queued_job.enqueue_time = std::chrono::system_clock::now();
    queued_job.dependencies = dependencies;

    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job_queue_.push(queued_job);
    }

    queue_cv_.notify_one();
    jobs_scheduled_++;

    return queued_job.job_id;
}

std::vector<QueuedJob> JobScheduler::getQueuedJobs() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queue_mutex_));

    // Copy queue to vector (this is inefficient but safe)
    auto temp_queue = job_queue_;
    std::vector<QueuedJob> result;

    while (!temp_queue.empty()) {
        result.push_back(temp_queue.top());
        temp_queue.pop();
    }

    return result;
}

std::unordered_map<std::string, std::any> JobScheduler::getStatistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["jobs_scheduled"] = jobs_scheduled_.load();
    stats["jobs_executed"] = jobs_executed_.load();
    stats["jobs_failed"] = jobs_failed_.load();
    stats["queue_size"] = job_queue_.size();
    stats["schedules_count"] = schedules_.size();
    stats["strategy"] = static_cast<int>(strategy_);

    return stats;
}

void JobScheduler::setSchedulingStrategy(SchedulingStrategy strategy) {
    strategy_ = strategy;

    // Rebuild queue with new comparator
    std::lock_guard<std::mutex> lock(queue_mutex_);
    std::vector<QueuedJob> temp_jobs;

    while (!job_queue_.empty()) {
        temp_jobs.push_back(job_queue_.top());
        job_queue_.pop();
    }

    job_queue_ = std::priority_queue<QueuedJob, std::vector<QueuedJob>, JobComparator>(
        JobComparator{strategy}
    );

    for (const auto& job : temp_jobs) {
        job_queue_.push(job);
    }
}

void JobScheduler::registerJobCompletionCallback(
    std::function<void(const std::string&, JobStatus)> callback) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    completion_callbacks_.push_back(std::move(callback));
}

std::optional<std::string> JobScheduler::triggerSchedule(const std::string& schedule_id) {
    std::lock_guard<std::mutex> lock(schedules_mutex_);
    auto it = schedules_.find(schedule_id);
    if (it == schedules_.end() || !it->second.enabled) {
        return std::nullopt;
    }

    // Create job config from schedule
    JobConfig job_config;
    job_config.job_id = it->second.job_config_id;
    job_config.job_name = "Scheduled job from " + schedule_id;
    job_config.pipeline_config_path = "tests/integration/test_data/yaml/test_config.yaml"; // Default config for testing
    job_config.parameters = it->second.parameters;

    // Submit job
    auto job_id = submitJob(job_config, JobPriority::NORMAL, it->second.dependencies);

    // Update last run time
    it->second.last_run = std::chrono::system_clock::now();

    return job_id;
}

void JobScheduler::schedulerLoop() {
    while (running_) {
        // Process scheduled jobs
        processScheduledJobs();

        // Process job queue
        processJobQueue();

        // Wait for next iteration
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_cv_.wait_for(lock, std::chrono::seconds(1), [this] {
            return !job_queue_.empty() || !running_;
        });
    }
}

void JobScheduler::processScheduledJobs() {
    auto now = std::chrono::system_clock::now();
    std::vector<std::string> to_trigger;

    {
        std::lock_guard<std::mutex> lock(schedules_mutex_);
        for (auto& [id, schedule] : schedules_) {
            if (schedule.enabled &&
                schedule.trigger_type == TriggerType::SCHEDULED &&
                schedule.next_run <= now) {
                to_trigger.push_back(id);

                // Update next run time
                schedule.next_run = calculateNextRunTime(
                    schedule.cron_expression,
                    now
                );
                schedule.last_run = now;
            }
        }
    }

    // Trigger jobs outside of lock
    for (const auto& schedule_id : to_trigger) {
        triggerSchedule(schedule_id);
    }
}

void JobScheduler::processJobQueue() {
    while (running_) {
        auto job_opt = getNextJob();
        if (!job_opt) {
            break;
        }

        auto job = *job_opt;

        // Check dependencies
        if (!checkDependencies(job)) {
            // Re-queue job
            std::lock_guard<std::mutex> lock(queue_mutex_);
            job_queue_.push(job);
            continue;
        }

        // Submit to job manager
        try {
            job_manager_->submitJob(job.job_config);

            // Call completion callback if set
            if (job.callback) {
                job.callback();
            }
        } catch (const std::exception& e) {
            // Log error and update statistics
            jobs_failed_++;
        }
    }
}

bool JobScheduler::checkDependencies(const QueuedJob& job) const {
    if (job.dependencies.empty()) {
        return true;
    }

    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(completed_mutex_));
    for (const auto& dep_id : job.dependencies) {
        auto it = completed_jobs_.find(dep_id);
        if (it == completed_jobs_.end() || it->second != JobStatus::Completed) {
            return false;
        }
    }

    return true;
}

std::chrono::system_clock::time_point JobScheduler::calculateNextRunTime(
    const std::string& cron_expr,
    std::chrono::system_clock::time_point from_time) const {

    // This is a simplified cron parser
    // In a real implementation, you'd use a proper cron library

    // Simple format: "* * * * *" (minute hour day month weekday)
    // For now, just support simple intervals

    if (cron_expr == "* * * * *") {
        // Every minute
        return from_time + std::chrono::minutes(1);
    } else if (cron_expr == "0 * * * *") {
        // Every hour
        return from_time + std::chrono::hours(1);
    } else if (cron_expr == "0 0 * * *") {
        // Every day
        return from_time + std::chrono::hours(24);
    } else if (cron_expr == "0 0 * * 0") {
        // Every week
        return from_time + std::chrono::hours(24 * 7);
    } else if (cron_expr == "0 0 1 * *") {
        // Every month (approximate)
        return from_time + std::chrono::hours(24 * 30);
    } else {
        // Default to daily
        return from_time + std::chrono::hours(24);
    }
}

std::optional<QueuedJob> JobScheduler::getNextJob() {
    std::lock_guard<std::mutex> lock(queue_mutex_);

    if (job_queue_.empty()) {
        return std::nullopt;
    }

    // Check if job manager can accept more jobs
    // Allow jobs to be submitted if there are available worker threads
    if (job_manager_->getActiveJobCount() >= 4) { // Use max concurrent jobs
        return std::nullopt;
    }

    auto job = job_queue_.top();
    job_queue_.pop();

    return job;
}

} // namespace omop::core